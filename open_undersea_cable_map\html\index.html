<!DOCTYPE html>
<html>
<head>
    <title>Submarine Cable Map</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        #map {
            height: 100vh;
            width: calc(100% - 350px); /* Adjust width for left sidebar */
            margin-left: 350px; /* Make room for left sidebar */
            position: relative;
        }

        /* Sidebar Styles - Positioned on the left */
        #sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 350px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid #e0e0e0;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            z-index: 1000;
            overflow-y: auto;
            padding: 20px;
            box-sizing: border-box;
        }

        #sidebar h2 {
            margin: 0 0 20px 0;
            color: #2c3e50;
            font-size: 20px;
            font-weight: 600;
        }

        .search-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e8e8e8;
        }

        .search-group {
            margin-bottom: 15px;
        }

        .search-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
            font-size: 14px;
        }

        .search-input {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .search-input:focus {
            outline: none;
            border-color: #3498db;
        }

        .dropdown-list {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e0e0e0;
            border-top: none;
            border-radius: 0 0 6px 6px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1001;
            display: none;
        }

        .dropdown-item {
            padding: 10px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .search-group {
            position: relative;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn {
            flex: 1;
            padding: 10px 15px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-primary:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .results-section {
            margin-top: 20px;
        }

        .results-header {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .cable-result {
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease, transform 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .cable-result:hover {
            border-color: #3498db;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
            transform: translateY(-1px);
        }

        .cable-result:active {
            transform: translateY(0px) scale(0.98);
        }

        .cable-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .cable-details {
            font-size: 12px;
            color: #7f8c8d;
            line-height: 1.4;
        }

        .cable-color-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            vertical-align: middle;
        }

        .no-results {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            padding: 20px;
        }

        .info {
            padding: 6px 8px;
            font: 14px/16px Arial, Helvetica, sans-serif;
            background: white;
            background: rgba(255,255,255,0.8);
            box-shadow: 0 0 15px rgba(0,0,0,0.2);
            border-radius: 5px;
        }
        .info h4 {
            margin: 0 0 5px;
            color: #777;
        }

        /* Responsive design for mobile devices */
        @media (max-width: 768px) {
            #sidebar {
                width: 300px; /* Slightly smaller on tablets */
            }

            #map {
                width: calc(100% - 300px);
                margin-left: 300px;
            }

            .cable-name-display {
                font-size: 12px;
                padding: 6px 10px;
                max-width: 250px;
            }
        }

        @media (max-width: 480px) {
            #sidebar {
                width: 100%;
                height: 35vh; /* Sidebar at top on mobile */
                position: relative;
                border-right: none;
                border-bottom: 1px solid #e0e0e0;
            }

            #map {
                width: 100%;
                margin-left: 0;
                height: 65vh; /* Map takes remaining space */
            }

            .search-section {
                margin-bottom: 15px;
                padding-bottom: 15px;
            }

            .search-group {
                margin-bottom: 10px;
            }

            .cable-name-display {
                font-size: 11px;
                padding: 5px 8px;
                max-width: 200px;
            }
        }

        /* Cable Name Label Styles */
        .cable-name-label {
            /* Remove default marker styling */
            background: none !important;
            border: none !important;
            /* Ensure visibility */
            z-index: 1000 !important;
            position: relative !important;
        }

        .cable-name-label-content {
            background: rgba(255, 255, 255, 0.95) !important;
            border: 2px solid #3498db !important;
            border-radius: 8px !important;
            padding: 8px 12px !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            font-size: 14px !important;
            font-weight: 600 !important;
            color: #2c3e50 !important;
            text-align: center !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
            white-space: nowrap !important;
            max-width: 200px !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            /* Ensure visibility */
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            /* Add a subtle animation */
            animation: cableLabelFadeIn 0.3s ease-out !important;
            /* Debug styling - temporary */
            min-height: 30px !important;
            min-width: 100px !important;
        }

        @keyframes cableLabelFadeIn {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Additional specificity for Leaflet markers */
        .leaflet-marker-icon.cable-name-label {
            z-index: 1000 !important;
        }

        .leaflet-marker-icon.cable-name-label .cable-name-label-content {
            background: rgba(255, 255, 255, 0.95) !important;
            border: 2px solid #3498db !important;
        }
    </style>
</head>
<body>
    <div id="sidebar">
        <h2>🌊 Cable Search</h2>

        <div class="search-section">
            <div class="search-group">
                <label for="fromCountry">From Country:</label>
                <input type="text" id="fromCountry" class="search-input" placeholder="Select or type country name..." autocomplete="off">
                <div id="fromCountryDropdown" class="dropdown-list"></div>
            </div>

            <div class="search-group">
                <label for="toCountry">To Country:</label>
                <input type="text" id="toCountry" class="search-input" placeholder="Select or type country name..." autocomplete="off">
                <div id="toCountryDropdown" class="dropdown-list"></div>
            </div>

            <div class="search-group">
                <label for="cableType">Cable Type:</label>
                <select id="cableType" class="search-input">
                    <option value="all">All</option>
                    <option value="main">Main</option>
                    <option value="secondary">Secondary</option>
                </select>
            </div>

            <div class="button-group">
                <button id="searchBtn" class="btn btn-primary" disabled>Search Cables</button>
                <button id="clearBtn" class="btn btn-secondary">Clear Search</button>
            </div>
        </div>


        <div class="results-section">
            <div id="resultsHeader" class="results-header" style="display: none;">Found Cables:</div>
            <div id="cableResults"></div>
        </div>
        <div id="legend" style="margin-top: 30px;">
            <h4 style="margin-bottom: 8px;">Legend</h4>
            <div style="display: flex; align-items: center; margin-bottom: 6px;">
                <span style="display:inline-block;width:24px;height:4px;background:#3498db;margin-right:8px;"></span>
                <span>Main Cable Route</span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 6px;">
                <span style="display:inline-block;width:24px;height:4px;background:#F18F01;border-bottom:2px dashed #F18F01;margin-right:8px;"></span>
                <span>Secondary Cable Route</span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 6px;">
                <span style="display:inline-block;width:12px;height:12px;background:#FFD700;border-radius:50%;margin-right:8px;"></span>
                <span>Highlighted Landing Point</span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 6px;">
                <span style="display:inline-block;width:12px;height:12px;background:#FF0000;border-radius:50%;margin-right:8px;"></span>
                <span>Landing Point</span>
            </div>
        </div>
    </div>

    <div id="map"></div>
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>

        const map = L.map('map', {
            worldCopyJump: false,
            maxBounds: [[-90, -180], [90, 180]],
            maxBoundsViscosity: 1.0,
            minZoom: 2,
            maxZoom: 8
        }).setView([20, 0], 2);

        L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
            attribution: '© OpenStreetMap contributors © CARTO',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(map);

        const cableLayer = L.layerGroup().addTo(map);
        const landingPointLayer = L.layerGroup().addTo(map);

        // Create info control for hover information
        const info = L.control();
        info.onAdd = function (map) {
            this._div = L.DomUtil.create('div', 'info');
            this.update();
            return this._div;
        };
        info.update = function (props) {
            this._div.innerHTML = '<h4>Submarine Cable Info</h4>' +  (props ?
                '<b>' + props.name + '</b><br />' +
                (props.rfs ? 'RFS: ' + props.rfs + '<br />' : '') +
                (props.length ? 'Length: ' + props.length + ' km<br />' : '') +
                (props.owners ? 'Owners: ' + props.owners : '')
                : 'Hover over a cable');
        };
        info.addTo(map);

        const professionalColorPalette = [
            '#2E86AB','#A23B72', '#F18F01',
            '#C73E1D','#6C757D', '#495057',
            '#4A90A4', '#8E44AD', '#D35400',
            '#27AE60', '#2C3E50', '#8B4513',
            '#556B2F', '#4682B4', '#CD853F',
            '#708090', '#2F4F4F', '#800080',
            '#B22222', '#228B22', '#4169E1',
            '#DC143C', '#FF8C00', '#9932CC',
            '#8FBC8F', '#483D8B', '#2E8B57',
            '#B8860B', '#A0522D', '#1E90FF',
            '#32CD32', '#FF6347','#4B0082',
            '#DAA520', '#008B8B', '#9400D3',
            '#FF4500', '#2E8B57', '#8B008B',
            '#556B2F'
        ];

        const specialCableColors = {
            'atlantic-crossing-1-ac-1': '#FF8C00',
            '2africa': '#000000',
            'africa-coast-to-europe-ace': '#DC143C',
            'west-africa-cable-system-wacs': '#4169E1',
            'maroc-telecom-west-africa': '#9932CC',
            'sat-3wasc': '#FF6347'
        };

        function getProfessionalCableColor(cableId, index) {
            if (specialCableColors.hasOwnProperty(cableId)) {
                console.log(`🎨 Applying special color for ${cableId}: ${specialCableColors[cableId]}`);
                return specialCableColors[cableId];
            }

            let hash = 0;
            for (let i = 0; i < cableId.length; i++) {
                const char = cableId.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32-bit integer
            }

            const colorIndex = (Math.abs(hash) + index) % professionalColorPalette.length;
            return professionalColorPalette[colorIndex];
        }


        // Americas countries (North, Central, and South America)
        const americasCountries = new Set([
            // North America
            'United States', 'Canada', 'Mexico', 'Greenland',
            'Guatemala', 'Belize', 'El Salvador', 'Honduras', 'Nicaragua', 'Costa Rica', 'Panama',
            'Cuba', 'Jamaica', 'Haiti', 'Dominican Republic', 'Puerto Rico', 'Bahamas', 'Barbados',
            'Trinidad and Tobago', 'Grenada', 'Saint Vincent and the Grenadines', 'Saint Lucia',
            'Dominica', 'Antigua and Barbuda', 'Saint Kitts and Nevis', 'Martinique', 'Guadeloupe',
            'Saint Barthélemy', 'Saint Martin', 'Sint Maarten', 'Anguilla', 'British Virgin Islands',
            'Virgin Islands (U.S.)', 'Virgin Islands (U.K.)', 'Cayman Islands', 'Turks and Caicos Islands',
            'Aruba', 'Curaçao', 'Bonaire, Sint Eustatius and Saba', 'Netherlands', 'French Guiana',
            'Brazil', 'Argentina', 'Chile', 'Peru', 'Colombia', 'Venezuela', 'Ecuador', 'Bolivia',
            'Paraguay', 'Uruguay', 'Guyana', 'Suriname', 'French Guiana'
        ]);

        // Define Asia-Pacific countries and territories
        const asiaPacificCountries = new Set([
            // East Asia
            'China', 'Japan', 'South Korea', 'North Korea', 'Taiwan', 'Hong Kong', 'Macau', 'Mongolia',
            // Southeast Asia
            'Singapore', 'Indonesia', 'Philippines', 'Malaysia', 'Vietnam', 'Thailand', 'Myanmar',
            'Cambodia', 'Laos', 'Brunei', 'Timor-Leste',
            // South Asia
            'India', 'Pakistan', 'Bangladesh', 'Sri Lanka', 'Nepal', 'Bhutan', 'Maldives', 'Afghanistan',
            // Oceania and Pacific Islands
            'Australia', 'New Zealand', 'Papua New Guinea', 'Fiji', 'Solomon Islands', 'Vanuatu',
            'New Caledonia', 'Samoa', 'Tonga', 'Kiribati', 'Tuvalu', 'Nauru', 'Palau', 'Marshall Islands',
            'Micronesia', 'Cook Islands', 'French Polynesia', 'Wallis and Futuna', 'American Samoa',
            'Guam', 'Northern Mariana Islands', 'Cocos (Keeling) Islands', 'Christmas Island'
        ]);

        // Function to check if a cable has landing points in the Americas
        function isAmericasCable(cableId) {
            return false;
        }
        // Function to check if coordinates are in Americas region (rough approximation)
        function isInAmericasRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    // Americas longitude range (including Alaska and eastern Brazil)
                    // Expanded range to be more inclusive: -180° to -25°
                    return lng >= -180 && lng <= -25;
                }

                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }
                return false;
            }
            return checkCoordinates(coordinates);
        }
        // Function to check if coordinates are in Asia-Pacific region
        function isInAsiaPacificRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            // Recursive function to check coordinates at any nesting level
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];

                    const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
                    const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);

                    return inMainAsiaPacific || inPacificExtension;
                }
                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }
                return false;
            }

            return checkCoordinates(coordinates);
        }
        // Fetch and display cable routes
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                console.log(`Total cables before filtering: ${data.features.length}`);

                // Critical cables that should always be preserved (African + important transatlantic)
                const criticalAfricanCables = new Set([
                    '2africa','west-africa-cable-system-wacs',
                    'africa-coast-to-europe-ace',
                    'eastern-africa-submarine-system-eassy',
                    'asia-africa-europe-1-aae-1',
                    'safe','sat-3wasc',
                    'equiano','africa-1',
                    'seychelles-to-east-africa-system-seas',
                    'the-east-african-marine-system-teams',
                    'seacomtata-tgn-eurasia',
                    'atlantic-crossing-1-ac-1'  // Important Europe-Americas cable (NL-UK-DE-US)
                ]);

                const filteredFeatures = data.features.filter(feature => {
                    // Always preserve critical African cables
                    if (criticalAfricanCables.has(feature.properties.id)) {
                        console.log(`Preserving critical African cable: ${feature.properties.name}`);
                        return true;
                    }

                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isInAmericasRegion(feature.geometry.coordinates);
                        const isAsiaPacific = isInAsiaPacificRegion(feature.geometry.coordinates);

                        if (isAmericas) {
                            console.log(`Filtering out Americas cable: ${feature.properties.name}`);
                            return false;
                        }
                        if (isAsiaPacific) {
                            console.log(`Filtering out Asia-Pacific cable: ${feature.properties.name}`);
                            return false;
                        }
                        return true;
                    }
                    return true;
                });

                console.log(`Total cables after filtering: ${filteredFeatures.length}`);

                // Create new GeoJSON object with filtered features
                const filteredData = {
                    ...data,
                    features: filteredFeatures
                };

                // Create cables with professional color scheme and interactive labeling
                L.geoJSON(filteredData, {
                    style: function(feature) {
                        const cableIndex = filteredData.features.indexOf(feature);
                        return getCableStyle(feature, cableIndex);
                    },
                    onEachFeature: function(feature, layer) {
                        // Register cable in the new direct reference system
                        registerCableLayer(layer, feature);

                        const cableIndex = filteredData.features.indexOf(feature);
                        const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);

                        layer.on({
                            mouseover: function(e) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 4,
                                    opacity: 1,
                                    color: originalColor
                                });
                                info.update(feature.properties);
                            },
                            mouseout: function(e) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 2.5,
                                    opacity: 0.85,
                                    color: originalColor
                                });
                                info.update();
                            },
                            click: function(e) {
                                // Show detailed popup instead of simple label
                                layer.openPopup();

                                // Prevent event bubbling to map click
                                L.DomEvent.stopPropagation(e);
                            }
                        });

                        // Create enhanced popup with professional styling
                        let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                        popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';
                        popupContent += '<div style="display: flex; align-items: center; margin-bottom: 6px;">';
                        popupContent += '<span style="color: ' + originalColor + '; font-size: 16px; margin-right: 6px;">●</span>';
                        popupContent += '<span style="font-size: 12px; color: #6c757d;">Cable Color</span>';
                        popupContent += '</div>';

                        if (feature.properties.rfs) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                        }
                        if (feature.properties.length) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                        }
                        if (feature.properties.owners) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                        }

                        popupContent += '</div>';

                        layer.bindPopup(popupContent);
                    }
                }).addTo(cableLayer);

                // Validate cable identification after loading
                setTimeout(() => {
                    validateCableIdentification();
                    console.log('Cable map loaded with enhanced sidebar functionality');
                }, 1000);
            });

        // Function to check if landing point coordinates are in Americas region
        function isLandingPointInAmericas(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            // Americas longitude range (including Alaska and eastern Brazil)
            return lng >= -180 && lng <= -25;
        }

        // Function to check if landing point coordinates are in Asia-Pacific region
        function isLandingPointInAsiaPacific(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            const lat = coordinates[1];

            // Main Asia-Pacific: Longitude 65°E to 180°E, Latitude -50°S to 80°N
            // Pacific extension: Longitude -180°E to -120°W (for Pacific islands), Latitude -50°S to 80°N
            const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
            const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);

            return inMainAsiaPacific || inPacificExtension;
        }

        // Fetch and display landing points
        fetch('../landing-point/landing-point-geo.json')
            .then(response => response.json())
            .then(data => {
                console.log(`Total landing points before filtering: ${data.features.length}`);

                // Critical African landing points that should always be preserved
                const criticalAfricanLandingPoints = new Set([
                    'cape-town-south-africa',
                    'mtunzini-south-africa',
                    'port-elizabeth-south-africa',
                    'gqeberha-south-africa',
                    'mombasa-kenya',
                    'dar-es-salaam-tanzania',
                    'djibouti-city-djibouti','lagos-nigeria',
                    'accra-ghana','dakar-senegal',
                    'casablanca-morocco',
                    'alexandria-egypt',
                    'port-said-egypt',
                    'zafarana-egypt',
                    'mumbai-india',
                    'maputo-mozambique',
                    'jeddah-saudi-arabia'
                ]);

                // Filter out landing points in Americas and Asia-Pacific regions, but preserve critical African infrastructure
                const filteredFeatures = data.features.filter(feature => {
                    // Always preserve critical African landing points
                    if (criticalAfricanLandingPoints.has(feature.properties.id)) {
                        console.log(`Preserving critical African landing point: ${feature.properties.name}`);
                        return true;
                    }

                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isLandingPointInAmericas(feature.geometry.coordinates);
                        const isAsiaPacific = isLandingPointInAsiaPacific(feature.geometry.coordinates);

                        if (isAmericas) {
                            console.log(`Filtering out Americas landing point: ${feature.properties.name}`);
                            return false;
                        }
                        if (isAsiaPacific) {
                            console.log(`Filtering out Asia-Pacific landing point: ${feature.properties.name}`);
                            return false;
                        }
                        return true;
                    }
                    return true;
                });

                console.log(`Total landing points after filtering: ${filteredFeatures.length}`);

                // Create new GeoJSON object with filtered features
                const filteredData = {
                    ...data,
                    features: filteredFeatures
                };

                L.geoJSON(filteredData, {
                    pointToLayer: (feature, latlng) => {
                        return L.circleMarker(latlng, {
                            radius: 5,
                            fillColor: '#FF0000',
                            color: '#000',
                            weight: 1,
                            opacity: 1,
                            fillOpacity: 0.8
                        });
                    },
                    onEachFeature: (feature, layer) => {
                        if (feature.properties.name) {
                            layer.bindPopup(`<b>${feature.properties.name}</b><br>
                                ${feature.properties.country || ''}`);
                        }
                    }
                }).addTo(landingPointLayer);
            });

        // Add layer control with improved tile options
        const baseMaps = {
            "CartoDB Positron": L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                attribution: '© OpenStreetMap contributors © CARTO',
                subdomains: 'abcd',
                maxZoom: 19
            }),
            "Esri World Street": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles © Esri — Source: Esri, DeLorme, NAVTEQ, USGS, Intermap, iPC, NRCAN, Esri Japan, METI, Esri China (Hong Kong), Esri (Thailand), TomTom, 2012',
                maxZoom: 19
            }),
            "CartoDB Voyager": L.tileLayer('https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png', {
                attribution: '© OpenStreetMap contributors © CARTO',
                subdomains: 'abcd',
                maxZoom: 19
            }),
            "OpenStreetMap": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 19
            }),
            "Satellite": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles © Esri — Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
                maxZoom: 19
            })
        };

        const overlayMaps = {
            "Submarine Cables": cableLayer,
            "Landing Points": landingPointLayer
        };

        L.control.layers(baseMaps, overlayMaps).addTo(map);

        L.control.scale().addTo(map);

        let allCables = [];
        let allLandingPoints = [];
        let availableCountries = new Set();
        let originalCableStyles = new Map();
        let isSearchActive = false;
        let currentCableIndex = 0;
        let cableCarouselActive = false;
        let allCableLayers = []; // Array to store all cable layers for easy reference
        let currentHighlightedCable = null; // Track currently highlighted cable
        let currentCableNameLabel = null; // Track current cable name label on map

        // ===================================================================
        // ROBUST CABLE SELECTION SYSTEM - DIRECT LAYER REFERENCES
        // ===================================================================
        let cableLayerRegistry = new Map(); // Direct mapping: cable ID -> layer reference
        let cableNameRegistry = new Map();  // Direct mapping: cable name -> layer reference
        let cableSearchRegistry = new Map(); // Direct mapping: search result -> layer reference

        // ===================================================================
        // CABLE REGISTRY FUNCTIONS - DIRECT LAYER REFERENCE SYSTEM
        // ===================================================================

        /**
         * Register a cable layer in all registries for direct access
         * @param {L.Layer} layer - The Leaflet layer
         * @param {Object} feature - The GeoJSON feature
         */
        function registerCableLayer(layer, feature) {
            if (!feature || !feature.properties) return;

            const cableId = feature.properties.id;
            const cableName = feature.properties.name;

            if (cableId) {
                cableLayerRegistry.set(cableId, layer);
                console.log(`📝 Registered cable by ID: ${cableId} -> ${cableName}`);
            }

            if (cableName) {
                // Register by exact name
                cableNameRegistry.set(cableName, layer);

                // Register by normalized name for flexible matching
                const normalizedName = normalizeCableName(cableName);
                cableNameRegistry.set(normalizedName, layer);

                console.log(`📝 Registered cable by name: "${cableName}" (normalized: "${normalizedName}")`);
            }

            // Add to allCableLayers array for backward compatibility
            allCableLayers.push(layer);
        }

        /**
         * Normalize cable name for flexible matching
         * @param {string} name - The cable name to normalize
         * @returns {string} - Normalized name
         */
        function normalizeCableName(name) {
            return name.toLowerCase()
                      .replace(/[\s\-_]+/g, '') // Remove spaces, hyphens, underscores
                      .replace(/[^\w]/g, ''); // Remove non-word characters
        }

        /**
         * Get cable layer by ID with guaranteed success
         * @param {string} cableId - The cable ID
         * @returns {L.Layer|null} - The cable layer or null
         */
        function getCableLayerById(cableId) {
            const layer = cableLayerRegistry.get(cableId);
            if (layer) {
                console.log(`✅ Found cable layer by ID: ${cableId}`);
                return layer;
            }
            console.warn(`⚠️ Cable layer not found by ID: ${cableId}`);
            return null;
        }

        /**
         * Get cable layer by name with flexible matching
         * @param {string} cableName - The cable name
         * @returns {L.Layer|null} - The cable layer or null
         */
        function getCableLayerByName(cableName) {
            // Try exact match first
            let layer = cableNameRegistry.get(cableName);
            if (layer) {
                console.log(`✅ Found cable layer by exact name: "${cableName}"`);
                return layer;
            }

            // Try normalized match
            const normalizedName = normalizeCableName(cableName);
            layer = cableNameRegistry.get(normalizedName);
            if (layer) {
                console.log(`✅ Found cable layer by normalized name: "${cableName}" -> "${normalizedName}"`);
                return layer;
            }

            console.warn(`⚠️ Cable layer not found by name: "${cableName}"`);
            return null;
        }

        /**
         * Clear all cable registries
         */
        function clearCableRegistries() {
            cableLayerRegistry.clear();
            cableNameRegistry.clear();
            cableSearchRegistry.clear();
            allCableLayers = [];
            console.log('🧹 Cleared all cable registries');
        }

        // Add map click handler to clear search if active
        map.on('click', function(e) {
            if (isSearchActive) {
                clearSearch();
            }
            // Clear cable name label when clicking elsewhere on the map
            clearCableNameLabel();
        });

        // Load cable and landing point data for sidebar
        Promise.all([
            fetch('../cable/cable-geo.json').then(response => response.json()),
            fetch('../landing-point/landing-point-geo.json').then(response => response.json())
        ]).then(([cableData, landingPointData]) => {
            allCables = cableData.features;
            allLandingPoints = landingPointData.features;

            extractAvailableCountries();
            setupSearchFunctionality();
        });

        function extractAvailableCountries() {
            const africaCountries = new Set([
                'Algeria', 'Angola', 'Benin', 'Botswana', 'Burkina Faso', 'Burundi', 'Cameroon',
                'Cape Verde', 'Central African Republic', 'Chad', 'Comoros', 'Congo', 'Congo, Dem. Rep.',
                'Congo, Rep.', 'Côte d\'Ivoire', 'Djibouti', 'Egypt', 'Equatorial Guinea', 'Eritrea',
                'Ethiopia', 'Gabon', 'Gambia', 'Ghana', 'Guinea', 'Guinea-Bissau', 'Kenya', 'Lesotho',
                'Liberia', 'Libya', 'Madagascar', 'Malawi', 'Mali', 'Mauritania', 'Mauritius', 'Morocco',
                'Mozambique', 'Namibia', 'Niger', 'Nigeria', 'Rwanda', 'São Tomé and Príncipe', 'Senegal',
                'Seychelles', 'Sierra Leone', 'Somalia', 'South Africa', 'South Sudan', 'Sudan', 'Swaziland',
                'Tanzania', 'Togo', 'Tunisia', 'Uganda', 'Zambia', 'Zimbabwe'
            ]);

            const europeCountries = new Set([
                'Albania', 'Andorra', 'Austria', 'Belarus', 'Belgium', 'Bosnia and Herzegovina', 'Bulgaria',
                'Croatia', 'Cyprus', 'Czech Republic', 'Denmark', 'Estonia', 'Finland', 'France', 'Germany',
                'Greece', 'Hungary', 'Iceland', 'Ireland', 'Italy', 'Latvia', 'Liechtenstein', 'Lithuania',
                'Luxembourg', 'Malta', 'Moldova', 'Monaco', 'Montenegro', 'Netherlands', 'North Macedonia',
                'Norway', 'Poland', 'Portugal', 'Romania', 'Russia', 'San Marino', 'Serbia', 'Slovakia',
                'Slovenia', 'Spain', 'Sweden', 'Switzerland', 'Ukraine', 'United Kingdom', 'Vatican City'
            ]);

            // Extract countries from landing points that are in Africa or Europe
            allLandingPoints.forEach(point => {
                if (point.properties && point.properties.name) {
                    const nameParts = point.properties.name.split(',');
                    if (nameParts.length >= 2) {
                        const country = nameParts[nameParts.length - 1].trim();
                        if (africaCountries.has(country) || europeCountries.has(country)) {
                            availableCountries.add(country);
                        }
                    }
                }
            });

            // Add some known countries that might be in the cable data but not easily extracted
            const knownAfricanEuropeanCountries = [
                'South Africa', 'Egypt', 'Morocco', 'Nigeria', 'Kenya', 'Ghana', 'Senegal', 'Tanzania',
                'Angola', 'Mozambique', 'Madagascar', 'Mauritius', 'Seychelles', 'Djibouti', 'Somalia',
                'United Kingdom', 'France', 'Spain', 'Italy', 'Germany', 'Netherlands', 'Portugal',
                'Greece', 'Norway', 'Denmark', 'Sweden', 'Finland', 'Ireland', 'Belgium', 'Malta',
                'Cyprus', 'Bulgaria', 'Romania', 'Croatia', 'Albania', 'Turkey', 'Russia'
            ];

            knownAfricanEuropeanCountries.forEach(country => {
                if (africaCountries.has(country) || europeCountries.has(country)) {
                    availableCountries.add(country);
                }
            });

            console.log('Available countries for search:', Array.from(availableCountries).sort());
        }

        function setupSearchFunctionality() {
            const fromCountryInput = document.getElementById('fromCountry');
            const toCountryInput = document.getElementById('toCountry');
            const fromDropdown = document.getElementById('fromCountryDropdown');
            const toDropdown = document.getElementById('toCountryDropdown');
            const searchBtn = document.getElementById('searchBtn');
            const clearBtn = document.getElementById('clearBtn');

            const countriesArray = Array.from(availableCountries).sort();

            // Setup autocomplete for both inputs
            setupAutocomplete(fromCountryInput, fromDropdown, countriesArray);
            setupAutocomplete(toCountryInput, toDropdown, countriesArray);

            // Enable search button when both countries are selected
            function updateSearchButton() {
                const fromValid = availableCountries.has(fromCountryInput.value);
                const toValid = availableCountries.has(toCountryInput.value);
                searchBtn.disabled = !(fromValid && toValid && fromCountryInput.value !== toCountryInput.value);

                // Auto-clear search when both fields are empty
                if (fromCountryInput.value === '' && toCountryInput.value === '' && isSearchActive) {
                    console.log('🔄 Auto-clearing search due to empty input fields');

                    // Brief visual feedback that search is being cleared
                    const resultsDiv = document.getElementById('cableResults');
                    if (resultsDiv) {
                        resultsDiv.innerHTML = '<div class="no-results">🔄 Restoring original map view...</div>';
                        setTimeout(() => {
                            clearSearchResults();
                        }, 300); // Small delay for visual feedback
                    } else {
                        clearSearchResults();
                    }
                }
            }

            fromCountryInput.addEventListener('input', updateSearchButton);
            toCountryInput.addEventListener('input', updateSearchButton);

            // Additional event listeners to catch all ways of clearing input
            fromCountryInput.addEventListener('keyup', updateSearchButton);
            toCountryInput.addEventListener('keyup', updateSearchButton);
            fromCountryInput.addEventListener('change', updateSearchButton);
            toCountryInput.addEventListener('change', updateSearchButton);

            // Search functionality
            searchBtn.addEventListener('click', performSearch);
            clearBtn.addEventListener('click', clearSearch);
        }

        function setupAutocomplete(input, dropdown, countries) {
            input.addEventListener('input', function() {
                const value = this.value.toLowerCase();
                dropdown.innerHTML = '';

                if (value.length === 0) {
                    dropdown.style.display = 'none';
                    return;
                }

                const filtered = countries.filter(country =>
                    country.toLowerCase().includes(value)
                ).slice(0, 10); // Limit to 10 results

                if (filtered.length > 0) {
                    filtered.forEach(country => {
                        const item = document.createElement('div');
                        item.className = 'dropdown-item';
                        item.textContent = country;
                        item.addEventListener('click', function() {
                            input.value = country;
                            dropdown.style.display = 'none';
                            input.dispatchEvent(new Event('input'));
                        });
                        dropdown.appendChild(item);
                    });
                    dropdown.style.display = 'block';
                } else {
                    dropdown.style.display = 'none';
                }
            });

            // Hide dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!input.contains(e.target) && !dropdown.contains(e.target)) {
                    dropdown.style.display = 'none';
                }
            });
        }

        function getCableStyle(feature, cableIndex) {
            const cableColor = getProfessionalCableColor(feature.properties.id, cableIndex);
            const priority = feature.properties.priority || 'main';
            if (priority === 'main') {
                return {
                    color: cableColor,
                    weight: 3.5,
                    opacity: 0.85,
                    dashArray: null
                };
            } else {
                // Secondary/alternative: orange dashed
                return {
                    color: '#F18F01',
                    weight: 2.5,
                    opacity: 0.85,
                    dashArray: '6, 6'
                };
            }
        }

        // --- ENHANCED: Show only matching cables on search ---
        let allCableGeoJSON = null;
        let currentCableGeoJSON = null;

        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                allCableGeoJSON = data;
            });

        // Helper to clear and add only matching cables
        function showOnlyCables(connectingCables) {
            cableLayer.clearLayers();
            if (!connectingCables || connectingCables.length === 0) return;
            // Build a GeoJSON with only the matching features
            const features = connectingCables.map(cable => cable);
            const filteredGeoJSON = {
                ...allCableGeoJSON,
                features: features.map(c => c.feature || c)
            };
            currentCableGeoJSON = filteredGeoJSON;
            L.geoJSON(filteredGeoJSON, {
                style: function(feature) {
                    const cableIndex = filteredGeoJSON.features.indexOf(feature);
                    return getCableStyle(feature, cableIndex);
                },
                onEachFeature: function(feature, layer) {
                    const cableIndex = filteredGeoJSON.features.indexOf(feature);
                    const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);
                    layer.on({
                        mouseover: function(e) {
                            const layer = e.target;
                            layer.setStyle({
                                weight: 4,
                                opacity: 1,
                                color: originalColor
                            });
                            info.update(feature.properties);
                        },
                        mouseout: function(e) {
                            const layer = e.target;
                            layer.setStyle({
                                weight: 2.5,
                                opacity: 0.85,
                                color: originalColor
                            });
                            info.update();
                        },
                        click: function(e) {
                            layer.openPopup();
                            L.DomEvent.stopPropagation(e);
                        }
                    });
                    let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                    popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';
                    popupContent += '<div style="display: flex; align-items: center; margin-bottom: 6px;">';
                    popupContent += '<span style="color: ' + originalColor + '; font-size: 16px; margin-right: 6px;">●</span>';
                    popupContent += '<span style="font-size: 12px; color: #6c757d;">Cable Color</span>';
                    popupContent += '</div>';
                    if (feature.properties.rfs) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                    }
                    if (feature.properties.length) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                    }
                    if (feature.properties.owners) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                    }
                    popupContent += '</div>';
                    layer.bindPopup(popupContent);
                }
            }).addTo(cableLayer);
        }

        // Helper to restore all cables
        function restoreAllCables() {
            cableLayer.clearLayers();
            if (!allCableGeoJSON) return;
            L.geoJSON(allCableGeoJSON, {
                style: function(feature) {
                    const cableIndex = allCableGeoJSON.features.indexOf(feature);
                    return getCableStyle(feature, cableIndex);
                },
                onEachFeature: function(feature, layer) {
                    // (reuse your onEachFeature code from above)
                    const cableIndex = allCableGeoJSON.features.indexOf(feature);
                    const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);
                    layer.on({
                        mouseover: function(e) {
                            const layer = e.target;
                            layer.setStyle({
                                weight: 4,
                                opacity: 1,
                                color: originalColor
                            });
                            info.update(feature.properties);
                        },
                        mouseout: function(e) {
                            const layer = e.target;
                            layer.setStyle({
                                weight: 2.5,
                                opacity: 0.85,
                                color: originalColor
                            });
                            info.update();
                        },
                        click: function(e) {
                            layer.openPopup();
                            L.DomEvent.stopPropagation(e);
                        }
                    });
                    let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                    popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';
                    popupContent += '<div style="display: flex; align-items: center; margin-bottom: 6px;">';
                    popupContent += '<span style="color: ' + originalColor + '; font-size: 16px; margin-right: 6px;">●</span>';
                    popupContent += '<span style="font-size: 12px; color: #6c757d;">Cable Color</span>';
                    popupContent += '</div>';
                    if (feature.properties.rfs) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                    }
                    if (feature.properties.length) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                    }
                    if (feature.properties.owners) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                    }
                    popupContent += '</div>';
                    layer.bindPopup(popupContent);
                }
            }).addTo(cableLayer);
        }
        // --- END ENHANCED ---

        async function performSearch() {
            const fromCountry = document.getElementById('fromCountry').value;
            const toCountry = document.getElementById('toCountry').value;
            const cableType = document.getElementById('cableType').value;

            console.log(`🔍 Searching for cables between ${fromCountry} and ${toCountry} (type: ${cableType})`);

            // Show loading state
            const searchBtn = document.getElementById('searchBtn');
            const originalText = searchBtn.textContent;
            searchBtn.textContent = 'Searching...';
            searchBtn.disabled = true;

            try {
                // Find cables connecting these countries
                let connectingCables = await findConnectingCables(fromCountry, toCountry);
                if (cableType !== 'all') {
                    connectingCables = connectingCables.filter(cable => {
                        const priority = (cable.cableData && cable.cableData.priority) || (cable.properties && cable.properties.priority) || 'main';
                        return priority === cableType;
                    });
                }

                console.log(`✅ Found ${connectingCables.length} connecting cables`);

                showOnlyCables(connectingCables);

                highlightLandingPointsForCables(connectingCables);

                // Display results in sidebar
                displaySearchResults(connectingCables, fromCountry, toCountry);

                isSearchActive = true;

                // Show success message
                if (connectingCables.length > 0) {
                    console.log(`🎯 Highlighted ${connectingCables.length} cables, made others barely visible (opacity 0.1)`);
                } else {
                    console.log(`❌ No cables found between ${fromCountry} and ${toCountry}`);
                }

            } catch (error) {
                console.error('Search failed:', error);
                document.getElementById('cableResults').innerHTML = '<div class="no-results">Search failed. Please try again.</div>';
            } finally {
                // Restore search button
                searchBtn.textContent = originalText;
                searchBtn.disabled = false;
            }
        }

        function clearSearchResults() {
            console.log('🔄 Clearing search results and restoring original view');

            // Hide dropdowns
            document.getElementById('fromCountryDropdown').style.display = 'none';
            document.getElementById('toCountryDropdown').style.display = 'none';

            // Hide results
            document.getElementById('resultsHeader').style.display = 'none';
            document.getElementById('cableResults').innerHTML = '';

            restoreAfricaEuropeCables();
            resetLandingPointHighlights();

            // Clear cable name label
            clearCableNameLabel();

            isSearchActive = false;

            console.log('✅ Search results cleared, map restored to original filtered state');
        }

        function clearSearch() {
            console.log('🧹 Clearing search and restoring original view');

            // Reset input fields
            document.getElementById('fromCountry').value = '';
            document.getElementById('toCountry').value = '';
            document.getElementById('searchBtn').disabled = true;

            // Clear search results (this will handle the visual reset)
            clearSearchResults();

            // Clear cable name label
            clearCableNameLabel();

            console.log('✅ Search cleared completely, all inputs and results reset');
        }

        async function findConnectingCables(fromCountry, toCountry) {
            const connectingCables = [];

            // Show loading indicator
            document.getElementById('cableResults').innerHTML = '<div class="no-results">Searching cables...</div>';

            // We need to load individual cable files to get landing point information
            const promises = allCables.map(async (cable) => {
                try {
                    const cableResponse = await fetch(`../cable/${cable.properties.id}.json`);
                    if (!cableResponse.ok) {
                        throw new Error(`HTTP ${cableResponse.status}`);
                    }
                    const cableData = await cableResponse.json();

                    if (cableData.landing_points && Array.isArray(cableData.landing_points)) {
                        const countries = cableData.landing_points.map(lp => lp.country).filter(Boolean);

                        if (countries.includes(fromCountry) && countries.includes(toCountry)) {
                            return {
                                ...cable,
                                cableData: cableData
                            };
                        }
                    }
                } catch (error) {
                    console.warn(`Could not load cable data for ${cable.properties.id}:`, error);
                }
                return null;
            });

            // Wait for all promises to resolve and filter out null results
            const results = await Promise.all(promises);
            const validResults = results.filter(result => result !== null);

            console.log(`Found ${validResults.length} cables connecting ${fromCountry} and ${toCountry}`);
            return validResults;
        }

        function updateMapVisualization(connectingCables) {
            console.log(`Highlighting ${connectingCables.length} cables and making others barely visible`);

            // Store original styles if not already stored
            if (originalCableStyles.size === 0) {
                cableLayer.eachLayer(layer => {
                    if (layer.feature) {
                        originalCableStyles.set(layer.feature.properties.id, {
                            color: layer.options.color,
                            opacity: layer.options.opacity,
                            weight: layer.options.weight
                        });
                    }
                });
            }

            const connectingCableIds = new Set(connectingCables.map(cable => cable.properties.id));

            // Update all cable layers with extreme visibility contrast (like submarinecablemap.com)
            cableLayer.eachLayer(layer => {
                if (layer.feature) {
                    const cableId = layer.feature.properties.id;

                    if (connectingCableIds.has(cableId)) {
                        const originalStyle = originalCableStyles.get(cableId);
                        layer.setStyle({
                            color: originalStyle.color, // Keep original bright color
                            opacity: 1.0, // Full opacity for maximum visibility
                            weight: 4, // Thicker for better visibility
                            dashArray: null // Remove any dashing for solid lines
                        });

                        // Bring highlighted cables to front
                        layer.bringToFront();

                        console.log(`Highlighted cable: ${layer.feature.properties.name} with color ${originalStyle.color}`);
                    } else {
                        // BARELY VISIBLE: Make all other cables almost invisible (like submarinecablemap.com)
                        layer.setStyle({
                            color: '#cccccc', // Very light gray color
                            opacity: 0.1, // Extremely low opacity - barely visible
                            weight: 1, // Very thin lines
                            dashArray: null
                        });
                    }
                }
            });

            // Count total cables for verification
            let totalCables = 0;
            let highlightedCount = 0;
            let dimmedCount = 0;

            cableLayer.eachLayer(layer => {
                if (layer.feature) {
                    totalCables++;
                    const cableId = layer.feature.properties.id;
                    if (connectingCableIds.has(cableId)) {
                        highlightedCount++;
                    } else {
                        dimmedCount++;
                    }
                }
            });

            console.log(`Map visualization updated:`);
            console.log(`- Total cables: ${totalCables}`);
            console.log(`- Highlighted (opacity 1.0): ${highlightedCount}`);
            console.log(`- Barely visible (opacity 0.1): ${dimmedCount}`);
            console.log(`- Contrast ratio: Highlighted cables are 10x more visible than others`);
        }

        function resetMapVisualization() {
            console.log('Resetting map visualization to original state');

            // Restore original styles with smooth transitions
            cableLayer.eachLayer(layer => {
                if (layer.feature && originalCableStyles.has(layer.feature.properties.id)) {
                    const originalStyle = originalCableStyles.get(layer.feature.properties.id);

                    // Apply original style smoothly
                    layer.setStyle({
                        color: originalStyle.color,
                        opacity: originalStyle.opacity,
                        weight: originalStyle.weight,
                        dashArray: layer.feature.properties.is_planned ? '8, 4' : null
                    });
                    if (layer.getPopup()) {
                        layer.closePopup();
                    }
                }
            });
        }

        function displaySearchResults(connectingCables, fromCountry, toCountry) {
            const resultsHeader = document.getElementById('resultsHeader');
            const cableResults = document.getElementById('cableResults');

            if (connectingCables.length === 0) {
                resultsHeader.style.display = 'block';
                resultsHeader.textContent = 'No cables found';
                cableResults.innerHTML = '<div class="no-results">No submarine cables found connecting these countries.</div>';
                return;
            }

            resultsHeader.style.display = 'block';
            resultsHeader.textContent = `Found ${connectingCables.length} cable${connectingCables.length > 1 ? 's' : ''}:`;

            cableResults.innerHTML = '';

            connectingCables.forEach(cable => {
                const cableDiv = document.createElement('div');
                cableDiv.className = 'cable-result';

                const actualCableColor = getActualCableColor(cable.properties.id);
                const cableData = cable.cableData;

                cableDiv.innerHTML = `
                    <div class="cable-name">
                        <span class="cable-color-indicator" style="background-color: ${actualCableColor}"></span>
                        ${cable.properties.name}
                    </div>
                    <div class="cable-details">
                        ${cableData.length ? `Length: ${cableData.length}` : ''}
                        ${cableData.rfs ? ` • RFS: ${cableData.rfs}` : ''}
                        ${cableData.owners ? `<br>Owners: ${cableData.owners}` : ''}
                    </div>
                `;

                // Add robust click handler using direct layer references
                cableDiv.addEventListener('click', () => {
                    console.log(`🖱️ Cable clicked: "${cable.properties.name}" (ID: ${cable.properties.id})`);

                    // Use the new robust selection system
                    selectCableFromSearchResult(cable, cableDiv);
                });

                cableResults.appendChild(cableDiv);
            });
        }

        function getActualCableColor(cableId) {
            let actualColor = '#3498db';

            cableLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties.id === cableId) {
                    if (originalCableStyles.has(cableId)) {
                        actualColor = originalCableStyles.get(cableId).color;
                    } else if (layer.options.color) {
                        actualColor = layer.options.color;
                    } else if (layer.feature.properties.color) {
                        actualColor = layer.feature.properties.color;
                    }

                    if (!actualColor || actualColor === 'undefined') {
                        actualColor = '#3498db';
                    }
                }
            });

            return actualColor;
        }

        function validateCableIdentification() {
            let identifiedCables = 0;
            let totalCables = 0;

            cableLayer.eachLayer(layer => {
                totalCables++;
                if (layer.feature && layer.feature.properties.id) {
                    identifiedCables++;
                } else {
                    console.warn('Cable layer without proper identification:', layer);
                }
            });

            console.log(`Cable identification: ${identifiedCables}/${totalCables} cables properly identified`);
            return identifiedCables === totalCables;
        }

        function addClickFeedback(element) {
            // Add a brief visual feedback when cable result is clicked
            element.style.transform = 'scale(0.98)';
            element.style.backgroundColor = '#e8f4fd';
            element.style.borderColor = '#3498db';

            setTimeout(() => {
                element.style.transform = 'scale(1)';
                element.style.backgroundColor = 'white';
                element.style.borderColor = '#e8e8e8';
            }, 200);
        }

        function centerMapOnCable(cable) {
            if (cable.geometry && cable.geometry.coordinates) {
                // Calculate bounds of the cable
                const coords = cable.geometry.coordinates;
                let bounds = L.latLngBounds();

                function addCoordinatesToBounds(coordinates) {
                    if (Array.isArray(coordinates[0])) {
                        if (Array.isArray(coordinates[0][0])) {
                            // MultiLineString
                            coordinates.forEach(lineString => {
                                lineString.forEach(coord => {
                                    bounds.extend([coord[1], coord[0]]);
                                });
                            });
                        } else {
                            // LineString
                            coordinates.forEach(coord => {
                                bounds.extend([coord[1], coord[0]]);
                            });
                        }
                    }
                }
                 addCoordinatesToBounds(coords);

                if (bounds.isValid()) {
                    // Enhanced map centering with better padding and zoom control
                    map.fitBounds(bounds, {
                        padding: [30, 30],
                        maxZoom: 6 // Prevent zooming in too much for very short cables
                    });
                }
            }
        }

        function highlightSpecificCable(cableId) {
            console.log(`🎯 Highlighting cable: ${cableId}`);

            let foundLayer = false;
            // Find and highlight the specific cable with enhanced visual effects
            cableLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties.id === cableId) {
                    foundLayer = true;
                    console.log(`✅ Found cable layer for ID: ${cableId}`);
                    const originalStyle = originalCableStyles.get(cableId);

                    // Create a pulsing highlight effect
                    const pulseHighlight = () => {
                        // First pulse - maximum highlight
                        layer.setStyle({
                            color: originalStyle.color,
                            opacity: 1.0,
                            weight: 8, // Thick line for maximum visibility
                            dashArray: null // Remove any dashing
                        });

                        // Add a subtle glow effect by creating a temporary shadow layer
                        setTimeout(() => {
                            layer.setStyle({
                                color: originalStyle.color,
                                opacity: 0.9,
                                weight: 7
                            });
                        }, 300);

                        setTimeout(() => {
                            layer.setStyle({
                                color: originalStyle.color,
                                opacity: 1.0,
                                weight: 8
                            });
                        }, 600);
                    };

                    // Start the pulse effect
                    pulseHighlight();

                    // Show popup with cable info if available
                    console.log(`🔍 Checking for popup on layer...`);
                    if (layer.getPopup()) {
                        console.log(`✅ Popup found, opening it...`);
                        // Find a good position for the popup (center of cable bounds)
                        const bounds = layer.getBounds();
                        if (bounds && bounds.isValid()) {
                            const center = bounds.getCenter();
                            console.log(`📍 Opening popup at center:`, center);
                            layer.openPopup(center);
                        } else {
                            console.log(`📍 Opening popup at default position`);
                            layer.openPopup();
                        }
                    } else {
                        console.warn(`⚠️ No popup found on cable layer for ID: ${cableId}`);
                    }

                    // Reset to search result state after 4 seconds
                    setTimeout(() => {
                        if (isSearchActive) {
                            // Return to search result highlighting (not original state)
                            layer.setStyle({
                                color: originalStyle.color,
                                opacity: 1.0,
                                weight: 3.5 // Back to search result weight
                            });
                        } else {
                            // If search was cleared, return to original state
                            layer.setStyle(originalStyle);
                        }

                        // Close popup after highlight period
                        if (layer.getPopup()) {
                            layer.closePopup();
                        }
                    }, 4000);

                    // Bring this layer to front for better visibility
                    layer.bringToFront();
                }
            });

            if (!foundLayer) {
                console.error(`❌ Cable layer not found for ID: ${cableId}`);
                console.log(`🔍 Available cable IDs:`);
                let availableIds = [];
                cableLayer.eachLayer(layer => {
                    if (layer.feature && layer.feature.properties) {
                        availableIds.push({
                            id: layer.feature.properties.id,
                            name: layer.feature.properties.name
                        });
                        console.log(`  - ID: ${layer.feature.properties.id}, Name: ${layer.feature.properties.name}`);
                    }
                });

                // Try to find by name as fallback
                console.log(`🔄 Attempting to find cable by name instead...`);
                cableLayer.eachLayer(layer => {
                    if (layer.feature && layer.feature.properties) {
                        // Try to match by name (case-insensitive)
                        const searchName = cableId.toLowerCase().replace(/-/g, ' ');
                        const layerName = layer.feature.properties.name.toLowerCase();

                        if (layerName.includes(searchName) || searchName.includes(layerName)) {
                            console.log(`✅ Found potential match by name: ${layer.feature.properties.name} (ID: ${layer.feature.properties.id})`);
                            foundLayer = true;

                            // Apply the highlighting and popup logic here
                            const originalStyle = originalCableStyles.get(layer.feature.properties.id);
                            if (originalStyle) {
                                // Create a pulsing highlight effect
                                const pulseHighlight = () => {
                                    layer.setStyle({
                                        color: originalStyle.color,
                                        opacity: 1.0,
                                        weight: 8,
                                        dashArray: null
                                    });

                                    setTimeout(() => {
                                        layer.setStyle({
                                            color: originalStyle.color,
                                            opacity: 0.9,
                                            weight: 7
                                        });
                                    }, 300);

                                    setTimeout(() => {
                                        layer.setStyle({
                                            color: originalStyle.color,
                                            opacity: 1.0,
                                            weight: 8
                                        });
                                    }, 600);
                                };

                                pulseHighlight();

                                // Show popup
                                if (layer.getPopup()) {
                                    const bounds = layer.getBounds();
                                    if (bounds && bounds.isValid()) {
                                        const center = bounds.getCenter();
                                        layer.openPopup(center);
                                        console.log(`✅ Popup opened for matched cable`);
                                    } else {
                                        layer.openPopup();
                                    }
                                } else {
                                    console.warn(`⚠️ No popup bound to matched cable`);
                                }

                                // Reset after 4 seconds
                                setTimeout(() => {
                                    if (isSearchActive) {
                                        layer.setStyle({
                                            color: originalStyle.color,
                                            opacity: 1.0,
                                            weight: 3.5
                                        });
                                    } else {
                                        layer.setStyle(originalStyle);
                                    }

                                    if (layer.getPopup()) {
                                        layer.closePopup();
                                    }
                                }, 4000);

                                layer.bringToFront();
                            }
                            return; // Exit the loop
                        }
                    }
                });
            }
        }

        // --- ENHANCED: Highlight landing points for searched cables ---
        let highlightedLandingPointIds = new Set();
        function highlightLandingPointsForCables(connectingCables) {
            highlightedLandingPointIds.clear();
            connectingCables.forEach(cable => {
                if (cable.cableData && cable.cableData.landing_points) {
                    cable.cableData.landing_points.forEach(lp => {
                        if (lp.id) highlightedLandingPointIds.add(lp.id);
                    });
                }
            });
            landingPointLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties && layer.feature.properties.id) {
                    if (highlightedLandingPointIds.has(layer.feature.properties.id)) {
                        layer.setStyle({ fillColor: '#FFD700', radius: 10 }); // Gold and larger
                        layer.bringToFront();
                    } else {
                        layer.setStyle({ fillColor: '#FF0000', radius: 5 }); // Default
                    }
                }
            });
        }
        function resetLandingPointHighlights() {
            highlightedLandingPointIds.clear();
            landingPointLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties && layer.feature.properties.id) {
                    layer.setStyle({ fillColor: '#FF0000', radius: 5 });
                }
            });
        }

        /**
         * Function to manually open cable popup - ensures popup is shown
         * @param {string} cableId - The ID of the cable to show popup for
         */
        function openCablePopup(cableId) {
            console.log(`🔍 Manually opening popup for cable ID: ${cableId}`);

            let popupOpened = false;
            cableLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties.id === cableId) {
                    console.log(`✅ Found cable layer for popup: ${layer.feature.properties.name}`);

                    if (layer.getPopup()) {
                        console.log(`📋 Popup exists, opening it...`);
                        // Get cable bounds for positioning
                        const bounds = layer.getBounds();
                        if (bounds && bounds.isValid()) {
                            const center = bounds.getCenter();
                            layer.openPopup(center);
                            console.log(`✅ Popup opened at center: ${center}`);
                        } else {
                            layer.openPopup();
                            console.log(`✅ Popup opened at default position`);
                        }
                        popupOpened = true;
                    } else {
                        console.warn(`⚠️ No popup bound to this cable layer`);
                        // Create a temporary popup if none exists
                        const tempPopup = `<div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                            <h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">${layer.feature.properties.name}</h4>
                            ${layer.feature.properties.length ? `<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ${layer.feature.properties.length} km</div>` : ''}
                            ${layer.feature.properties.owners ? `<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ${layer.feature.properties.owners}</div>` : ''}
                        </div>`;

                        layer.bindPopup(tempPopup);
                        const bounds = layer.getBounds();
                        if (bounds && bounds.isValid()) {
                            const center = bounds.getCenter();
                            layer.openPopup(center);
                        } else {
                            layer.openPopup();
                        }
                        console.log(`✅ Created and opened temporary popup`);
                        popupOpened = true;
                    }
                }
            });

            if (!popupOpened) {
                console.log(`🔄 Trying to find cable by name fallback...`);

                // Try to find by name as fallback
                cableLayer.eachLayer(layer => {
                    if (layer.feature && layer.feature.properties && !popupOpened) {
                        const searchName = cableId.toLowerCase().replace(/-/g, ' ');
                        const layerName = layer.feature.properties.name.toLowerCase();

                        if (layerName.includes(searchName) || searchName.includes(layerName)) {
                            console.log(`✅ Found cable by name fallback: ${layer.feature.properties.name}`);

                            if (layer.getPopup()) {
                                const bounds = layer.getBounds();
                                if (bounds && bounds.isValid()) {
                                    const center = bounds.getCenter();
                                    layer.openPopup(center);
                                } else {
                                    layer.openPopup();
                                }
                                console.log(`✅ Popup opened via name fallback`);
                                popupOpened = true;
                            } else {
                                // Create temporary popup
                                const tempPopup = `<div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                                    <h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">${layer.feature.properties.name}</h4>
                                    ${layer.feature.properties.length ? `<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ${layer.feature.properties.length} km</div>` : ''}
                                    ${layer.feature.properties.owners ? `<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ${layer.feature.properties.owners}</div>` : ''}
                                </div>`;

                                layer.bindPopup(tempPopup);
                                const bounds = layer.getBounds();
                                if (bounds && bounds.isValid()) {
                                    const center = bounds.getCenter();
                                    layer.openPopup(center);
                                } else {
                                    layer.openPopup();
                                }
                                console.log(`✅ Created and opened temporary popup via name fallback`);
                                popupOpened = true;
                            }
                        }
                    }
                });

                if (!popupOpened) {
                    console.error(`❌ Could not open popup for cable ID: ${cableId} even with name fallback`);
                }
            }

            return popupOpened;
        }

        /**
         * Find cable layer by name (more flexible matching)
         * @param {string} cableName - The cable name to search for
         * @returns {Object|null} - The matching cable layer and properties or null
         */
        function findCableByName(cableName) {
            console.log(`🔍 Searching for cable by name: "${cableName}"`);

            let foundMatch = null;
            let matchType = '';

            cableLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties && !foundMatch) {
                    const layerName = layer.feature.properties.name;

                    // Try exact match first (case-insensitive)
                    if (layerName.toLowerCase() === cableName.toLowerCase()) {
                        console.log(`✅ Exact name match found: "${layerName}"`);
                        foundMatch = { layer, properties: layer.feature.properties };
                        matchType = 'exact';
                        return;
                    }

                    // Try exact match with trimmed whitespace
                    if (layerName.trim().toLowerCase() === cableName.trim().toLowerCase()) {
                        console.log(`✅ Trimmed exact match found: "${layerName}"`);
                        foundMatch = { layer, properties: layer.feature.properties };
                        matchType = 'trimmed';
                        return;
                    }
                }
            });

            // If no exact match, try partial matches
            if (!foundMatch) {
                cableLayer.eachLayer(layer => {
                    if (layer.feature && layer.feature.properties && !foundMatch) {
                        const layerName = layer.feature.properties.name;

                        // Try partial match (search name contains layer name or vice versa)
                        if (layerName.toLowerCase().includes(cableName.toLowerCase())) {
                            console.log(`✅ Partial match found (layer contains search): "${layerName}"`);
                            foundMatch = { layer, properties: layer.feature.properties };
                            matchType = 'partial-layer-contains-search';
                            return;
                        }

                        if (cableName.toLowerCase().includes(layerName.toLowerCase())) {
                            console.log(`✅ Partial match found (search contains layer): "${layerName}"`);
                            foundMatch = { layer, properties: layer.feature.properties };
                            matchType = 'partial-search-contains-layer';
                            return;
                        }
                    }
                });
            }

            // If still no match, try normalized matching
            if (!foundMatch) {
                cableLayer.eachLayer(layer => {
                    if (layer.feature && layer.feature.properties && !foundMatch) {
                        const layerName = layer.feature.properties.name;

                        // Normalize both names (remove spaces, hyphens, underscores, numbers at start)
                        const normalizeString = (str) => {
                            return str.toLowerCase()
                                     .replace(/^[\d\s\-_]+/, '') // Remove leading numbers, spaces, hyphens, underscores
                                     .replace(/[\s\-_]+/g, '') // Remove all spaces, hyphens, underscores
                                     .replace(/[^\w]/g, ''); // Remove non-word characters
                        };

                        const normalizedLayerName = normalizeString(layerName);
                        const normalizedSearchName = normalizeString(cableName);

                        if (normalizedLayerName && normalizedSearchName &&
                            (normalizedLayerName === normalizedSearchName ||
                             normalizedLayerName.includes(normalizedSearchName) ||
                             normalizedSearchName.includes(normalizedLayerName))) {
                            console.log(`✅ Normalized match found: "${layerName}" (normalized: "${normalizedLayerName}" vs "${normalizedSearchName}")`);
                            foundMatch = { layer, properties: layer.feature.properties };
                            matchType = 'normalized';
                            return;
                        }
                    }
                });
            }

            if (foundMatch) {
                console.log(`🎯 Match type: ${matchType}`);
            }

            return foundMatch;
        }

        /**
         * Improved function to highlight cable by name instead of ID
         * @param {string} cableName - The cable name to highlight
         */
        function highlightCableByName(cableName) {
            console.log(`🎯 Highlighting cable by name: "${cableName}"`);

            const match = findCableByName(cableName);

            if (match) {
                const { layer, properties } = match;
                console.log(`✅ Found cable: ${properties.name} (ID: ${properties.id})`);

                // Use the existing highlight logic
                const originalStyle = originalCableStyles.get(properties.id);
                if (originalStyle) {
                    // Pulse effect
                    const pulseHighlight = () => {
                        layer.setStyle({
                            color: originalStyle.color,
                            opacity: 1.0,
                            weight: 8,
                            dashArray: null
                        });

                        setTimeout(() => {
                            layer.setStyle({
                                color: originalStyle.color,
                                opacity: 0.9,
                                weight: 7
                            });
                        }, 300);

                        setTimeout(() => {
                            layer.setStyle({
                                color: originalStyle.color,
                                opacity: 1.0,
                                weight: 8
                            });
                        }, 600);
                    };

                    pulseHighlight();

                    // Show popup
                    if (layer.getPopup()) {
                        const bounds = layer.getBounds();
                        if (bounds && bounds.isValid()) {
                            const center = bounds.getCenter();
                            layer.openPopup(center);
                            console.log(`✅ Popup opened for: ${properties.name}`);
                        } else {
                            layer.openPopup();
                        }
                    } else {
                        console.warn(`⚠️ No popup bound, creating temporary one`);
                        // Create temporary popup
                        const tempPopup = `<div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                            <h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">${properties.name}</h4>
                            ${properties.length ? `<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ${properties.length} km</div>` : ''}
                            ${properties.owners ? `<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ${properties.owners}</div>` : ''}
                        </div>`;

                        layer.bindPopup(tempPopup);
                        const bounds = layer.getBounds();
                        if (bounds && bounds.isValid()) {
                            const center = bounds.getCenter();
                            layer.openPopup(center);
                        } else {
                            layer.openPopup();
                        }
                        console.log(`✅ Created and opened temporary popup`);
                    }

                    // Reset after 4 seconds
                    setTimeout(() => {
                        if (isSearchActive) {
                            layer.setStyle({
                                color: originalStyle.color,
                                opacity: 1.0,
                                weight: 3.5
                            });
                        } else {
                            layer.setStyle(originalStyle);
                        }

                        if (layer.getPopup()) {
                            layer.closePopup();
                        }
                    }, 4000);

                    layer.bringToFront();
                    return true;
                }
            } else {
                console.error(`❌ Cable not found by name: "${cableName}"`);
                console.log(`🔍 Available cables for comparison:`);

                // Show available cables for debugging
                const availableCables = [];
                cableLayer.eachLayer(layer => {
                    if (layer.feature && layer.feature.properties && layer.feature.properties.name) {
                        availableCables.push(layer.feature.properties.name);
                    }
                });

                console.log(`📋 Total cables loaded: ${availableCables.length}`);
                availableCables.sort().forEach((name, index) => {
                    console.log(`  ${index + 1}. "${name}"`);
                });

                // Try to find similar names
                const similarNames = availableCables.filter(name =>
                    name.toLowerCase().includes(cableName.toLowerCase()) ||
                    cableName.toLowerCase().includes(name.toLowerCase()) ||
                    name.toLowerCase().replace(/[\s\-_]/g, '').includes(cableName.toLowerCase().replace(/[\s\-_]/g, ''))
                );

                if (similarNames.length > 0) {
                    console.log(`🔍 Similar cable names found:`);
                    similarNames.forEach(name => console.log(`  - "${name}"`));
                }

                return false;
            }
        }

        // ===================================================================
        // ROBUST CABLE SELECTION SYSTEM - MAIN FUNCTIONS
        // ===================================================================

        /**
         * Robust cable selection function that guarantees success
         * @param {Object} searchResult - The cable from search results
         * @param {HTMLElement} clickedElement - The clicked sidebar element
         */
        function selectCableFromSearchResult(searchResult, clickedElement) {
            console.log(`🎯 Selecting cable from search result: "${searchResult.properties.name}" (ID: ${searchResult.properties.id})`);

            // Add visual feedback to clicked element
            addClickFeedback(clickedElement);

            // Center map on cable
            centerMapOnCable(searchResult);

            // Find the actual layer using our registry system
            let targetLayer = null;

            // Try ID-based lookup first
            if (searchResult.properties.id) {
                targetLayer = getCableLayerById(searchResult.properties.id);
            }

            // If ID lookup failed, try name-based lookup
            if (!targetLayer && searchResult.properties.name) {
                targetLayer = getCableLayerByName(searchResult.properties.name);
            }

            // If we found the layer, highlight and show popup
            if (targetLayer) {
                console.log(`✅ Found target layer for cable: ${searchResult.properties.name}`);
                highlightAndShowPopup(targetLayer, searchResult.properties);
                return true;
            } else {
                console.error(`❌ Could not find layer for cable: ${searchResult.properties.name}`);
                // Create a fallback popup at the cable center
                createFallbackPopup(searchResult);
                return false;
            }
        }

        /**
         * Highlight cable layer and show popup
         * @param {L.Layer} layer - The cable layer
         * @param {Object} cableProperties - Cable properties from search result
         */
        function highlightAndShowPopup(layer, cableProperties) {
            console.log(`🎨 Highlighting and showing popup for: ${cableProperties.name}`);

            // Get original style
            const originalStyle = originalCableStyles.get(cableProperties.id);

            if (originalStyle) {
                // Apply pulsing highlight effect
                const pulseHighlight = () => {
                    layer.setStyle({
                        color: originalStyle.color,
                        opacity: 1.0,
                        weight: 8,
                        dashArray: null
                    });

                    setTimeout(() => {
                        layer.setStyle({
                            color: originalStyle.color,
                            opacity: 0.9,
                            weight: 7
                        });
                    }, 300);

                    setTimeout(() => {
                        layer.setStyle({
                            color: originalStyle.color,
                            opacity: 1.0,
                            weight: 8
                        });
                    }, 600);
                };

                pulseHighlight();

                // Show popup
                showCablePopup(layer, cableProperties);

                // Reset highlighting after 4 seconds
                setTimeout(() => {
                    resetCableHighlighting(layer, originalStyle);
                }, 4000);

                // Bring layer to front
                layer.bringToFront();
            }
        }

        /**
         * Show popup for cable layer
         * @param {L.Layer} layer - The cable layer
         * @param {Object} cableProperties - Cable properties
         */
        function showCablePopup(layer, cableProperties) {
            console.log(`📋 Showing popup for: ${cableProperties.name}`);

            if (layer.getPopup()) {
                // Use existing popup
                const bounds = layer.getBounds();
                if (bounds && bounds.isValid()) {
                    const center = bounds.getCenter();
                    layer.openPopup(center);
                    console.log(`✅ Opened existing popup at center`);
                } else {
                    layer.openPopup();
                    console.log(`✅ Opened existing popup at default position`);
                }
            } else {
                // Create new popup
                console.log(`🔧 Creating new popup for cable`);
                createAndBindPopup(layer, cableProperties);

                const bounds = layer.getBounds();
                if (bounds && bounds.isValid()) {
                    const center = bounds.getCenter();
                    layer.openPopup(center);
                } else {
                    layer.openPopup();
                }
                console.log(`✅ Created and opened new popup`);
            }
        }

        /**
         * Create and bind popup to layer
         * @param {L.Layer} layer - The cable layer
         * @param {Object} cableProperties - Cable properties
         */
        function createAndBindPopup(layer, cableProperties) {
            const popupContent = `<div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                <h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">${cableProperties.name}</h4>
                ${cableProperties.length ? `<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ${cableProperties.length} km</div>` : ''}
                ${cableProperties.rfs ? `<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ${cableProperties.rfs}</div>` : ''}
                ${cableProperties.owners ? `<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ${cableProperties.owners}</div>` : ''}
            </div>`;

            layer.bindPopup(popupContent);
        }

        /**
         * Reset cable highlighting to search state or original state
         * @param {L.Layer} layer - The cable layer
         * @param {Object} originalStyle - Original cable style
         */
        function resetCableHighlighting(layer, originalStyle) {
            if (isSearchActive) {
                // Return to search result highlighting
                layer.setStyle({
                    color: originalStyle.color,
                    opacity: 1.0,
                    weight: 3.5
                });
            } else {
                // Return to original state
                layer.setStyle(originalStyle);
            }

            // Close popup
            if (layer.getPopup()) {
                layer.closePopup();
            }
        }

        /**
         * Create fallback popup when layer is not found
         * @param {Object} searchResult - The search result cable
         */
        function createFallbackPopup(searchResult) {
            console.log(`🔧 Creating fallback popup for: ${searchResult.properties.name}`);

            // Calculate center point from cable geometry
            let centerPoint = null;
            if (searchResult.geometry && searchResult.geometry.coordinates) {
                const coords = searchResult.geometry.coordinates;
                if (coords.length > 0) {
                    const midIndex = Math.floor(coords.length / 2);
                    const midCoord = coords[midIndex];
                    centerPoint = L.latLng(midCoord[1], midCoord[0]);
                }
            }

            if (centerPoint) {
                // Create a temporary marker with popup
                const popupContent = `<div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                    <h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">${searchResult.properties.name}</h4>
                    <div style="margin: 4px 0; font-size: 12px; color: #e74c3c;"><strong>Note:</strong> Cable layer not found on map</div>
                    ${searchResult.cableData && searchResult.cableData.length ? `<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ${searchResult.cableData.length}</div>` : ''}
                </div>`;

                const tempMarker = L.marker(centerPoint, {
                    opacity: 0 // Invisible marker
                }).addTo(map);

                tempMarker.bindPopup(popupContent).openPopup();

                // Remove marker after 4 seconds
                setTimeout(() => {
                    map.removeLayer(tempMarker);
                }, 4000);

                console.log(`✅ Created fallback popup at: ${centerPoint}`);
            }
        }
        // --- END ENHANCED ---

        // --- CABLE NAME LABEL FUNCTIONALITY ---
        /**
         * Create and display a cable name label on the map
         * @param {string} cableName - The name of the cable to display
         * @param {L.Layer} cableLayer - The cable layer to position the label near
         */
        function showCableNameLabel(cableName, cableLayer) {
            try {
                console.log(`🏷️ Creating cable name label for: "${cableName}"`);

                // Clear any existing label first
                clearCableNameLabel();

                // Calculate the center position of the cable route
                const labelPosition = calculateCableLabelPosition(cableLayer);

                if (!labelPosition) {
                    console.warn('⚠️ Could not calculate label position for cable:', cableName);
                    return;
                }

                console.log(`📍 Label position calculated:`, labelPosition);

                // Create the label as a Leaflet marker with custom HTML
                const labelIcon = L.divIcon({
                    className: 'cable-name-label',
                    html: `<div class="cable-name-label-content">${cableName}</div>`,
                    iconSize: [200, 30], // Width, height
                    iconAnchor: [100, 15] // Center the label
                });

                console.log(`🎨 Label icon created for: "${cableName}"`);

                // Create and add the marker to the map
                currentCableNameLabel = L.marker(labelPosition, {
                    icon: labelIcon,
                    interactive: false, // Don't interfere with map interactions
                    zIndexOffset: 1000 // Ensure it appears above other elements
                }).addTo(map);

                console.log(`✅ Cable name label "${cableName}" successfully added to map at position:`, labelPosition);

                // Verify the label was added
                if (currentCableNameLabel) {
                    console.log(`🔍 Label verification: currentCableNameLabel exists`);
                } else {
                    console.error(`❌ Label verification failed: currentCableNameLabel is null`);
                }

            } catch (error) {
                console.error('💥 Error showing cable name label:', error);
                console.error('Error details:', error.stack);
            }
        }

        /**
         * Calculate the best position for the cable name label
         * @param {L.Layer} cableLayer - The cable layer
         * @returns {L.LatLng|null} - The position for the label or null if calculation fails
         */
        function calculateCableLabelPosition(cableLayer) {
            try {
                if (!cableLayer.getBounds) {
                    console.warn('⚠️ Cable layer does not support getBounds');
                    return null;
                }

                const bounds = cableLayer.getBounds();

                if (!bounds || !bounds.isValid()) {
                    console.warn('⚠️ Invalid bounds for cable layer');
                    return null;
                }

                // Use the center of the cable bounds as the label position
                const center = bounds.getCenter();

                console.log('📐 Calculated label position at cable center:', center);
                return center;

            } catch (error) {
                console.error('💥 Error calculating cable label position:', error);
                return null;
            }
        }

        /**
         * Clear the current cable name label from the map
         */
        function clearCableNameLabel() {
            if (currentCableNameLabel) {
                console.log('🧹 Clearing cable name label');
                map.removeLayer(currentCableNameLabel);
                currentCableNameLabel = null;
            }
        }

        /**
         * Show cable name label for a specific cable by finding it in the cable layers
         * @param {string} cableName - The name of the cable
         * @param {string} cableId - The ID of the cable to find
         */
        function showCableNameLabelForCable(cableName, cableId) {
            try {
                console.log(`🔍 Looking for cable layer: "${cableName}" (ID: ${cableId})`);

                // Count total layers for debugging
                let totalLayers = 0;
                cableLayer.eachLayer(() => totalLayers++);
                console.log(`📊 Total cable layers available: ${totalLayers}`);

                // Find the cable layer by ID
                let foundLayer = null;
                cableLayer.eachLayer(layer => {
                    if (layer.feature &&
                        layer.feature.properties &&
                        layer.feature.properties.id === cableId) {
                        foundLayer = layer;
                        console.log(`✅ Found matching layer for ID: ${cableId}`);
                        return; // Break out of eachLayer iteration
                    }
                });

                if (foundLayer) {
                    console.log(`📍 Found cable layer for label: "${cableName}"`);
                    showCableNameLabel(cableName, foundLayer);
                } else {
                    console.warn(`⚠️ Could not find cable layer for: "${cableName}" (ID: ${cableId})`);
                    console.log('🔍 Available cable IDs:');
                    cableLayer.eachLayer(layer => {
                        if (layer.feature && layer.feature.properties) {
                            console.log(`  - ID: ${layer.feature.properties.id}, Name: ${layer.feature.properties.name}`);
                        }
                    });
                }

            } catch (error) {
                console.error('💥 Error showing cable name label for cable:', error);
            }
        }
        // --- END CABLE NAME LABEL FUNCTIONALITY ---

        // Store the filtered Africa/Europe cables GeoJSON
        let filteredAfricaEuropeGeoJSON = null;

        // When you first filter and display Africa/Europe cables, save that filtered GeoJSON
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                // Critical cables that should always be preserved (African + important transatlantic)
                const criticalAfricanCables = new Set([
                    '2africa',
                    'west-africa-cable-system-wacs',
                    'africa-coast-to-europe-ace',
                    'eastern-africa-submarine-system-eassy',
                    'asia-africa-europe-1-aae-1',
                    'safe',
                    'sat-3wasc',
                    'equiano',
                    'africa-1',
                    'seychelles-to-east-africa-system-seas',
                    'the-east-african-marine-system-teams',
                    'seacomtata-tgn-eurasia',
                    'atlantic-crossing-1-ac-1'  // Important Europe-Americas cable (NL-UK-DE-US)
                ]);
                // Filter out Americas and Asia-Pacific cables, keep only Africa/Europe and critical cables
                const filteredFeatures = data.features.filter(feature => {
                    if (criticalAfricanCables.has(feature.properties.id)) return true;
                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isInAmericasRegion(feature.geometry.coordinates);
                        const isAsiaPacific = isInAsiaPacificRegion(feature.geometry.coordinates);
                        if (isAmericas || isAsiaPacific) return false;
                        return true;
                    }
                    return true;
                });
                filteredAfricaEuropeGeoJSON = {
                    ...data,
                    features: filteredFeatures
                };
                // Create cables with professional color scheme and interactive labeling
                L.geoJSON(filteredAfricaEuropeGeoJSON, {
                    style: function(feature) {
                        const cableIndex = filteredAfricaEuropeGeoJSON.features.indexOf(feature);
                        return getCableStyle(feature, cableIndex);
                    },
                    onEachFeature: function(feature, layer) {
                        // Store original style for hover effects
                        const cableIndex = filteredAfricaEuropeGeoJSON.features.indexOf(feature);
                        const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);

                        layer.on({
                            mouseover: function(e) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 4,
                                    opacity: 1,
                                    color: originalColor
                                });
                                info.update(feature.properties);
                            },
                            mouseout: function(e) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 2.5,
                                    opacity: 0.85,
                                    color: originalColor
                                });
                                info.update();
                            },
                            click: function(e) {
                                layer.openPopup();
                                L.DomEvent.stopPropagation(e);
                            }
                        });

                        let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                        popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';
                        popupContent += '<div style="display: flex; align-items: center; margin-bottom: 6px;">';
                        popupContent += '<span style="color: ' + originalColor + '; font-size: 16px; margin-right: 6px;">●</span>';
                        popupContent += '<span style="font-size: 12px; color: #6c757d;">Cable Color</span>';
                        popupContent += '</div>';

                        if (feature.properties.rfs) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                        }
                        if (feature.properties.length) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                        }
                        if (feature.properties.owners) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                        }

                        popupContent += '</div>';

                        layer.bindPopup(popupContent);
                    }
                }).addTo(cableLayer);
            });

        // Function to restore the Africa/Europe filtered cables
        function restoreAfricaEuropeCables() {
            cableLayer.clearLayers();
            if (!filteredAfricaEuropeGeoJSON) return;
            L.geoJSON(filteredAfricaEuropeGeoJSON, {
                style: function(feature) {
                    const cableIndex = filteredAfricaEuropeGeoJSON.features.indexOf(feature);
                    return getCableStyle(feature, cableIndex);
                },
                onEachFeature: function(feature, layer) {
                    const cableIndex = filteredAfricaEuropeGeoJSON.features.indexOf(feature);
                    const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);
                    layer.on({
                        mouseover: function(e) {
                            const layer = e.target;
                            layer.setStyle({
                                weight: 4,
                                opacity: 1,
                                color: originalColor
                            });
                            info.update(feature.properties);
                        },
                        mouseout: function(e) {
                            const layer = e.target;
                            layer.setStyle({
                                weight: 2.5,
                                opacity: 0.85,
                                color: originalColor
                            });
                            info.update();
                        },
                        click: function(e) {
                            layer.openPopup();
                            L.DomEvent.stopPropagation(e);
                        }
                    });
                    let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                    popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';
                    popupContent += '<div style="display: flex; align-items: center; margin-bottom: 6px;">';
                    popupContent += '<span style="color: ' + originalColor + '; font-size: 16px; margin-right: 6px;">●</span>';
                    popupContent += '<span style="font-size: 12px; color: #6c757d;">Cable Color</span>';
                    popupContent += '</div>';
                    if (feature.properties.rfs) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                    }
                    if (feature.properties.length) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                    }
                    if (feature.properties.owners) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                    }
                    popupContent += '</div>';
                    layer.bindPopup(popupContent);
                }
            }).addTo(cableLayer);
        }

        // ===================================================================
        // TEST FUNCTIONS FOR DEBUGGING POPUP FUNCTIONALITY
        // ===================================================================

        // Test popup functionality for a specific cable
        window.testCablePopup = function(cableName) {
            console.log(`🧪 Testing popup for cable: "${cableName}"`);

            // Find cable by name
            let foundCable = null;
            cableLayer.eachLayer(layer => {
                if (layer.feature &&
                    layer.feature.properties &&
                    layer.feature.properties.name === cableName) {
                    foundCable = layer.feature.properties;
                    return;
                }
            });

            if (foundCable) {
                console.log(`✅ Found cable: ${foundCable.name} (ID: ${foundCable.id})`);
                openCablePopup(foundCable.id);
            } else {
                console.error(`❌ Cable not found: "${cableName}"`);
                console.log('Available cables:');
                cableLayer.eachLayer(layer => {
                    if (layer.feature && layer.feature.properties) {
                        console.log(`  - ${layer.feature.properties.name}`);
                    }
                });
            }
        };

        // Test highlighting and popup together
        window.testCableHighlightAndPopup = function(cableName) {
            console.log(`🧪 Testing highlight and popup for: "${cableName}"`);

            let foundCable = null;
            cableLayer.eachLayer(layer => {
                if (layer.feature &&
                    layer.feature.properties &&
                    layer.feature.properties.name === cableName) {
                    foundCable = layer.feature.properties;
                    return;
                }
            });

            if (foundCable) {
                highlightSpecificCable(foundCable.id);
                setTimeout(() => {
                    openCablePopup(foundCable.id);
                }, 500);
            } else {
                console.error(`❌ Cable not found: "${cableName}"`);
            }
        };

        // Check if cables have popups bound
        window.checkCablePopups = function() {
            console.log('🔍 Checking which cables have popups bound...');
            let totalCables = 0;
            let cablesWithPopups = 0;

            cableLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties) {
                    totalCables++;
                    if (layer.getPopup()) {
                        cablesWithPopups++;
                        console.log(`✅ ${layer.feature.properties.name} (ID: ${layer.feature.properties.id}) - has popup`);
                    } else {
                        console.log(`❌ ${layer.feature.properties.name} (ID: ${layer.feature.properties.id}) - NO popup`);
                    }
                }
            });

            console.log(`📊 Summary: ${cablesWithPopups}/${totalCables} cables have popups bound`);
            return { total: totalCables, withPopups: cablesWithPopups };
        };

        // Test the new name-based highlighting
        window.testNameHighlight = function(cableName) {
            console.log(`🧪 Testing name-based highlighting for: "${cableName}"`);
            return highlightCableByName(cableName);
        };

        // Find cable by name (for debugging)
        window.findCable = function(cableName) {
            console.log(`🔍 Finding cable: "${cableName}"`);
            return findCableByName(cableName);
        };

        // List all cable names and IDs
        window.listAllCables = function() {
            console.log('📋 All available cables:');
            const cables = [];
            cableLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties) {
                    const cable = {
                        name: layer.feature.properties.name,
                        id: layer.feature.properties.id
                    };
                    cables.push(cable);
                    console.log(`  - "${cable.name}" (ID: ${cable.id})`);
                }
            });
            return cables;
        };

        // Debug function specifically for the 2Africa cable issue
        window.debug2Africa = function() {
            console.log('🔍 Debugging 2Africa cable search...');

            const searchTerm = "2Africa";
            console.log(`🎯 Searching for: "${searchTerm}"`);

            // List all cables that might match
            const allCables = [];
            cableLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties && layer.feature.properties.name) {
                    allCables.push(layer.feature.properties.name);
                }
            });

            console.log(`📊 Total cables loaded: ${allCables.length}`);

            // Look for cables containing "africa" or "2"
            const africaCables = allCables.filter(name =>
                name.toLowerCase().includes('africa') ||
                name.toLowerCase().includes('2') ||
                name.toLowerCase().includes('two')
            );

            console.log(`🌍 Cables containing "africa", "2", or "two":`);
            africaCables.forEach(name => console.log(`  - "${name}"`));

            // Try the search function
            console.log(`🔍 Testing findCableByName("${searchTerm}"):`);
            const result = findCableByName(searchTerm);

            if (result) {
                console.log(`✅ Found: "${result.properties.name}" (ID: ${result.properties.id})`);
            } else {
                console.log(`❌ Not found`);
            }

            // Try variations
            const variations = ["2Africa", "2 Africa", "2-Africa", "Two Africa", "TwoAfrica", "africa"];
            console.log(`🔄 Trying variations:`);
            variations.forEach(variation => {
                const match = findCableByName(variation);
                if (match) {
                    console.log(`  ✅ "${variation}" -> "${match.properties.name}"`);
                } else {
                    console.log(`  ❌ "${variation}" -> not found`);
                }
            });
        };

        // Search for cables by partial name
        window.searchCables = function(searchTerm) {
            console.log(`🔍 Searching for cables containing: "${searchTerm}"`);

            const matches = [];
            cableLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties && layer.feature.properties.name) {
                    const name = layer.feature.properties.name;
                    if (name.toLowerCase().includes(searchTerm.toLowerCase())) {
                        matches.push({
                            name: name,
                            id: layer.feature.properties.id
                        });
                    }
                }
            });

            console.log(`📋 Found ${matches.length} matches:`);
            matches.forEach(match => {
                console.log(`  - "${match.name}" (ID: ${match.id})`);
            });

            return matches;
        };

        // ===================================================================
        // TEST FUNCTIONS FOR NEW ROBUST CABLE SELECTION SYSTEM
        // ===================================================================

        // Test the cable registry system
        window.testCableRegistry = function() {
            console.log('🧪 Testing cable registry system...');

            console.log(`📊 Registry Statistics:`);
            console.log(`  - Cable ID registry: ${cableLayerRegistry.size} entries`);
            console.log(`  - Cable name registry: ${cableNameRegistry.size} entries`);
            console.log(`  - All cable layers: ${allCableLayers.length} entries`);

            // Show some examples
            console.log(`📋 Sample registered cables by ID:`);
            let count = 0;
            for (const [id, layer] of cableLayerRegistry) {
                if (count < 5) {
                    console.log(`  - ID: ${id} -> ${layer.feature.properties.name}`);
                    count++;
                }
            }

            console.log(`📋 Sample registered cables by name:`);
            count = 0;
            for (const [name, layer] of cableNameRegistry) {
                if (count < 5 && layer.feature && layer.feature.properties) {
                    console.log(`  - Name: "${name}" -> ${layer.feature.properties.name}`);
                    count++;
                }
            }
        };

        // Test cable lookup by ID
        window.testCableLookupById = function(cableId) {
            console.log(`🔍 Testing cable lookup by ID: "${cableId}"`);
            const layer = getCableLayerById(cableId);
            if (layer) {
                console.log(`✅ Found: ${layer.feature.properties.name}`);
                return layer;
            } else {
                console.log(`❌ Not found`);
                return null;
            }
        };

        // Test cable lookup by name
        window.testCableLookupByName = function(cableName) {
            console.log(`🔍 Testing cable lookup by name: "${cableName}"`);
            const layer = getCableLayerByName(cableName);
            if (layer) {
                console.log(`✅ Found: ${layer.feature.properties.name} (ID: ${layer.feature.properties.id})`);
                return layer;
            } else {
                console.log(`❌ Not found`);
                return null;
            }
        };

        // Test the robust selection system with a mock search result
        window.testRobustSelection = function(cableName) {
            console.log(`🧪 Testing robust selection for: "${cableName}"`);

            // Find the cable in our registry
            const layer = getCableLayerByName(cableName);
            if (!layer) {
                console.error(`❌ Cable not found: ${cableName}`);
                return false;
            }

            // Create a mock search result
            const mockSearchResult = {
                properties: {
                    id: layer.feature.properties.id,
                    name: layer.feature.properties.name
                },
                geometry: layer.feature.geometry,
                cableData: {
                    length: layer.feature.properties.length,
                    rfs: layer.feature.properties.rfs,
                    owners: layer.feature.properties.owners
                }
            };

            // Create a mock clicked element
            const mockElement = document.createElement('div');
            mockElement.textContent = cableName;

            // Test the selection
            return selectCableFromSearchResult(mockSearchResult, mockElement);
        };

        // Test the 2Africa cable specifically
        window.test2Africa = function() {
            console.log('🧪 Testing 2Africa cable with new system...');

            // Try different variations
            const variations = ['2Africa', '2africa', '2 Africa', 'africa'];

            for (const variation of variations) {
                console.log(`🔍 Trying: "${variation}"`);
                const layer = getCableLayerByName(variation);
                if (layer) {
                    console.log(`✅ Found with variation "${variation}": ${layer.feature.properties.name}`);
                    return testRobustSelection(layer.feature.properties.name);
                }
            }

            console.log('❌ 2Africa cable not found with any variation');

            // Show what Africa-related cables we do have
            console.log('🌍 Available Africa-related cables:');
            for (const [name, layer] of cableNameRegistry) {
                if (name.toLowerCase().includes('africa') && layer.feature && layer.feature.properties) {
                    console.log(`  - "${layer.feature.properties.name}" (ID: ${layer.feature.properties.id})`);
                }
            }

            return false;
        };

        // Test functions for debugging - can be called from browser console
        window.testCableNameLabel = function(cableName) {
            console.log(`🧪 Testing cable name label for: "${cableName}"`);

            // Find cable by name
            let foundLayer = null;
            cableLayer.eachLayer(layer => {
                if (layer.feature &&
                    layer.feature.properties &&
                    layer.feature.properties.name === cableName) {
                    foundLayer = layer;
                    return;
                }
            });

            if (foundLayer) {
                showCableNameLabel(cableName, foundLayer);
                console.log(`✅ Label shown for: "${cableName}"`);
            } else {
                console.error(`❌ Cable not found: "${cableName}"`);
                console.log('Available cables:');
                cableLayer.eachLayer(layer => {
                    if (layer.feature && layer.feature.properties) {
                        console.log(`  - ${layer.feature.properties.name}`);
                    }
                });
            }
        };

        // Simple test to create a label at a fixed position
        window.testSimpleLabel = function() {
            console.log('🧪 Testing simple label creation');

            // Clear any existing label
            clearCableNameLabel();

            // Create a test label at a fixed position (center of map view)
            const testPosition = map.getCenter();
            console.log('Test position:', testPosition);

            const labelIcon = L.divIcon({
                className: 'cable-name-label',
                html: `<div class="cable-name-label-content">TEST LABEL</div>`,
                iconSize: [200, 30],
                iconAnchor: [100, 15]
            });

            currentCableNameLabel = L.marker(testPosition, {
                icon: labelIcon,
                interactive: false,
                zIndexOffset: 1000
            }).addTo(map);

            console.log('✅ Test label created');
        };

        window.clearTestLabel = function() {
            console.log('🧹 Clearing test label');
            clearCableNameLabel();
        };

        window.listAllCableNames = function() {
            console.log('📋 Available cable names:');
            const cableNames = [];
            cableLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties && layer.feature.properties.name) {
                    cableNames.push(layer.feature.properties.name);
                }
            });
            cableNames.sort().forEach((name, index) => {
                console.log(`${index + 1}. ${name}`);
            });
            return cableNames;
        };

        window.getCableLayerCount = function() {
            console.log(`📊 Total cable layers: ${allCableLayers.length}`);
            return allCableLayers.length;
        };
    </script>
</body>
</html>
